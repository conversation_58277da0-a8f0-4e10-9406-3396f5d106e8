import { BackgroundPanel } from "@/components/editor/BackgroundPanel"
import { BottomToolbar } from "@/components/editor/BottomToolbar"
import { CanvasEditor } from "@/components/editor/CanvasEditor"
import { ImagePanel } from "@/components/editor/ImagePanel"
import { LayerToolbar } from "@/components/editor/LayerToolbar"
import { PageNavigation } from "@/components/editor/PageNavigation"
import { PropertyPanel } from "@/components/editor/PropertyPanel"
import { SelectedImageToolbar } from "@/components/editor/SelectedImageToolbar"
import { SelectedTextToolbar } from "@/components/editor/SelectedTextToolbar"
import { StickerPanel } from "@/components/editor/StickerPanel"
import { TopToolbar } from "@/components/editor/TopToolbar"
import type { ShapeModel } from "@/components/pikaso/index.all"
import { ImageModel } from "@/components/pikaso/shape/models/ImageModel"
import type { LabelModel } from "@/components/pikaso/shape/models/LabelModel"
import { createImageFromUrl } from "@/components/pikaso/utils/create-image-from-url"
import usePikaso from "@/hooks/usePikaso"
import { useAlbumStore } from "@/stores/albumStore"
import { useEditorStore } from "@/stores/editorStore"
import type { PageContent } from "@/types/album"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import type Konva from "konva"
import { Settings } from "lucide-react"
import { useEffect, useRef, useState } from "react"
import { Drawer } from "vaul"

export const Route = createFileRoute("/_public/editor")({
  component: RouteComponent,
})

// 海报编辑器主组件
function RouteComponent() {
  const navigate = useNavigate()

  // 使用 albumStore 来同步保存数据
  const { updatePage: updateAlbumPage } = useAlbumStore()

  // 使用Zustand store
  const {
    editor,
    ref,
    canvasSize,
    selectedShapes,
    fileInputRef,
    backgroundPanelOpen,
    propertyPanelOpen,
    isMobile,
    currentPageId,
    pages,
    setEditor,
    setRef,
    setSelectedShapes,
    setFileInputRef,
    setBackgroundPanelOpen,
    setPropertyPanelOpen,
    setIsMobile,
    addImage,
    addText,
    addShape,
    setBackground,
    deleteSelected,
    duplicateSelected,
    changeLayerOrder,
    undo,
    redo,
    reset,
    initializeEditor,
    savePage,
    loadPage,
    generateThumbnail,
    setEditorData,
  } = useEditorStore()
  const [imagePanelOpen, setImagePanelOpen] = useState(false)
  const [stickerPanelOpen, setStickerPanelOpen] = useState(false)
  const [replaceTarget, setReplaceTarget] = useState<ImageModel | null>(null)
  console.log("🚀 ~ selectedShapes:", selectedShapes[0])
  // 从 albumStore 获取数据
  const { album } = useAlbumStore()

  // 创建引用
  const fileInputRefLocal = useRef<HTMLInputElement | null>(null)
  const [pikasoRef, pikasoEditor] = usePikaso({
    // width: isMobile ? undefined : canvasSize.width,
    // height: isMobile ? undefined : canvasSize.height,
    disableCanvasContextMenu: false,
    containerClassName: isMobile
      ? "bg-white"
      : "!top-auto !left-auto !translate-none !scale-100 bg-white",
    snapToGrid: {
      strokeWidth: 1,
      stroke: "#e5e7eb",
    },
    selection: {
      transformer: {
        // 移动端优化：增大控制点大小
        anchorSize:
          typeof window !== "undefined" && window.innerWidth < 768 ? 12 : 8,
        anchorStroke: "#4f46e5",
        anchorFill: "#ffffff",
        anchorStrokeWidth: 2,
        borderStroke: "#4f46e5",
        borderStrokeWidth: 2,
        // 移动端启用旋转
        rotateEnabled: true,
        // 移动端优化触摸操作
        enabledAnchors:
          typeof window !== "undefined" && window.innerWidth < 768
            ? ["top-left", "top-right", "bottom-left", "bottom-right"] // 移动端只显示四角
            : [
                "top-left",
                "top-right",
                "bottom-left",
                "bottom-right",
                "top-center",
                "bottom-center",
                "middle-left",
                "middle-right",
              ],
      },
    },
  })

  // 初始化编辑器
  useEffect(() => {
    initializeEditor()
    setFileInputRef(fileInputRefLocal as React.RefObject<HTMLInputElement>)
    setRef(pikasoRef)
    setEditor(pikasoEditor)
  }, [
    initializeEditor,
    setFileInputRef,
    setRef,
    setEditor,
    pikasoEditor,
    pikasoRef,
  ])

  // 监听选择变化
  useEffect(() => {
    if (!pikasoEditor) return

    // 使用更具体的类型
    const handleSelectionChange = (event: { shapes?: ShapeModel[] }) => {
      if (event.shapes) {
        setSelectedShapes(event.shapes)
      } else {
        setSelectedShapes([]) // 确保在取消选择时清空
      }
    }

    pikasoEditor.events.on("selection:change", handleSelectionChange)

    return () => {
      pikasoEditor.events.off("selection:change", handleSelectionChange)
    }
  }, [pikasoEditor, setSelectedShapes])

  // 处理图片上传
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    const file = files[0]
    const reader = new FileReader()

    reader.onload = (e) => {
      const dataUrl = e.target?.result as string
      if (dataUrl) {
        addImage(dataUrl, {
          x: Math.random() * (canvasSize.width - 200),
          y: Math.random() * (canvasSize.height - 200),
        })
      }
    }

    reader.readAsDataURL(file)

    // 清空文件输入
    if (fileInputRefLocal.current) {
      fileInputRefLocal.current.value = ""
    }
  }

  // 处理添加文本
  const handleAddText = () => {
    addText("双击编辑文本", {
      x: canvasSize.width / 2 - 100,
      y: canvasSize.height / 2,
      fontSize: 24,
      fill: "#000000",
      lineHeight: 1.2,
    })
  }

  // 处理保存页面
  const handleSavePage = async () => {
    if (!editor || !currentPageId) return

    try {
      // 生成缩略图
      const thumbnail = await generateThumbnail()

      // 获取画布数据
      const canvasData = editor.export.toJson()

      // 保存到 editorStore
      savePage(currentPageId, JSON.stringify(canvasData), thumbnail)

      // 同时保存到 albumStore - 使用类型断言来处理类型不匹配
      updateAlbumPage(currentPageId, {
        content: canvasData as unknown as PageContent, // 类型转换：Pikaso JsonData -> PageContent
        thumbnail: thumbnail,
      })

      console.log("页面保存成功")
    } catch (error) {
      console.error("保存页面失败:", error)
    }
  }

  // 页面切换逻辑
  const handlePrevPage = async () => {
    if (!album?.pages || !currentPageId) return

    // 先保存当前页面
    await handleSavePage()

    const currentIndex = album.pages.findIndex((p) => p.id === currentPageId)
    if (currentIndex > 0) {
      const prevPage = album.pages[currentIndex - 1]
      setEditorData(album.pages, prevPage.id)
    }
  }

  const handleNextPage = async () => {
    if (!album?.pages || !currentPageId) return

    // 先保存当前页面
    await handleSavePage()

    const currentIndex = album.pages.findIndex((p) => p.id === currentPageId)
    if (currentIndex < album.pages.length - 1) {
      const nextPage = album.pages[currentIndex + 1]
      setEditorData(album.pages, nextPage.id)
    }
  }

  // 计算是否可以切换页面
  const canGoPrev =
    album?.pages && currentPageId
      ? album.pages.findIndex((p) => p.id === currentPageId) > 0
      : false
  const canGoNext =
    album?.pages && currentPageId
      ? album.pages.findIndex((p) => p.id === currentPageId) <
        album.pages.length - 1
      : false

  // 获取当前页面信息
  const getCurrentPageInfo = () => {
    if (!album?.pages || !currentPageId) return ""

    const currentIndex = album.pages.findIndex((p) => p.id === currentPageId)
    if (currentIndex === -1) return ""

    const totalPages = album.pages.length
    return `第 ${currentIndex + 1} 页 / 共 ${totalPages} 页`
  }

  // 加载当前页面数据
  useEffect(() => {
    if (!editor || !currentPageId) return

    const pageData = loadPage(currentPageId)
    if (pageData?.content) {
      try {
        // editor.load() 接受字符串参数
        editor.load(JSON.stringify(pageData.content))
        console.log("页面数据加载成功:", pageData)
      } catch (error) {
        console.error("加载页面数据失败:", error)
      }
    } else {
      console.log("未找到页面数据，pageId:", currentPageId)
    }
  }, [editor, currentPageId, loadPage])

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={fileInputRefLocal}
        style={{ display: "none" }}
        accept="image/*"
        onChange={handleImageUpload}
      />

      {/* 顶部工具栏（集成返回与页面名） */}

      <TopToolbar
        editor={editor}
        onSave={handleSavePage}
        onPreview={() => {
          // 此处可根据需要实现预览逻辑
          // 例如跳转到预览页或弹窗
          console.log("预览功能待实现")
        }}
      />

      {/* 主内容区域 */}
      <div className="flex-1 flex relative">
        {/* 左侧画布 */}
        {selectedShapes.length > 0 && (
          <LayerToolbar onLayerChange={changeLayerOrder} />
        )}
        <div className="flex-1 flex flex-col md:flex-row ">
          <CanvasEditor
            ref={pikasoRef}
            editor={editor}
            canvasSize={canvasSize}
          />
          {/* 右侧属性面板 - 在桌面端显示 */}
          <div className="hidden md:block">
            <PropertyPanel
              selectedShapes={selectedShapes}
              onDelete={deleteSelected}
              onDuplicate={duplicateSelected}
            />
          </div>
        </div>

        {/* 移动端属性按钮 */}
        {isMobile && selectedShapes.length > 0 && (
          <button
            type="button"
            onClick={() => setPropertyPanelOpen(true)}
            className="fixed top-20 right-4 z-10 bg-blue-500 text-white p-3 rounded-full shadow-lg hover:bg-blue-600 transition-colors"
          >
            <Settings size={20} />
          </button>
        )}
      </div>
      <PageNavigation
        onPrevPage={handlePrevPage}
        onNextPage={handleNextPage}
        canGoPrev={canGoPrev}
        canGoNext={canGoNext}
        currentPageInfo={getCurrentPageInfo()}
      />
      {(() => {
        if (selectedShapes.length > 0 && editor) {
          const firstShape = selectedShapes[0]
          if (firstShape instanceof ImageModel) {
            return (
              <SelectedImageToolbar
                editor={editor}
                onDelete={deleteSelected}
                onCaption={() => console.log("字幕功能待实现")}
                onReplace={() => {
                  const firstShape = selectedShapes[0]
                  if (firstShape instanceof ImageModel) {
                    // 将当前图片设置为替换目标并打开图片面板
                    setReplaceTarget(firstShape)
                    setImagePanelOpen(true)
                  }
                }}
                onSwap={() => console.log("交换功能待实现")}
                onScale={() =>
                  console.log("缩放功能待实现 - 通常由变换器处理, 或特定逻辑")
                }
                onFillPage={() => console.log("填充页面功能待实现")}
                isMobile={isMobile}
              />
            )
          }
          if (firstShape.type === "label") {
            // 正确处理LabelModel类型
            // 添加类型断言，确保firstShape被视为LabelModel
            const labelShape = firstShape as unknown as LabelModel
            return (
              <SelectedTextToolbar
                editor={editor}
                selectedShape={labelShape}
                onDelete={deleteSelected}
                onEditText={() => {
                  // 触发文本编辑模式
                  console.log("编辑文本功能待实现")
                }}
                onChangeText={(newText) => {
                  if ("updateText" in firstShape) {
                    // 如果是LabelModel，使用updateText方法
                    ;(firstShape as LabelModel).updateText({
                      text: newText,
                    })
                  } else {
                    // 如果是TextModel，使用update方法
                    firstShape.update({ text: newText })
                  }
                }}
                onChangeFontSize={(delta) => {
                  // 获取当前字体大小
                  let currentSize = 20
                  if ("textNode" in firstShape) {
                    // 如果是LabelModel
                    currentSize =
                      (firstShape as LabelModel).textNode.fontSize() || 20
                  } else {
                    // 如果是TextModel
                    currentSize =
                      (firstShape.node as Konva.Text).fontSize() || 20
                  }

                  // 更新字体大小
                  if ("updateText" in firstShape) {
                    // 如果是LabelModel
                    ;(firstShape as LabelModel).updateText({
                      fontSize: Math.max(8, currentSize + delta),
                    })
                  } else {
                    // 如果是TextModel
                    firstShape.update({
                      fontSize: Math.max(8, currentSize + delta),
                    })
                  }
                }}
                onChangeColor={(color) => {
                  if ("updateText" in firstShape) {
                    // 如果是LabelModel
                    ;(firstShape as LabelModel).updateText({ fill: color })
                  } else {
                    // 如果是TextModel
                    firstShape.update({ fill: color })
                  }
                }}
                onChangePadding={(delta) => {
                  if ("textNode" in firstShape) {
                    const curr =
                      (firstShape as LabelModel).textNode.padding() || 0
                    ;(firstShape as LabelModel).updateText({
                      padding: Math.max(0, curr + delta),
                    })
                  } else {
                    const curr = (firstShape.node as Konva.Text).padding() || 0
                    firstShape.update({
                      padding: Math.max(0, curr + delta),
                    })
                  }
                }}
                isMobile={isMobile}
              />
            )
          }
        }
        return (
          <BottomToolbar
            onAddImage={() => setImagePanelOpen(true)}
            onAddText={handleAddText}
            onAddShape={(type) =>
              addShape(type, {
                x: canvasSize.width / 2 - 50,
                y: canvasSize.height / 2 - 50,
              })
            }
            onOpenBackgrounds={() => setBackgroundPanelOpen(true)}
            onOpenTemplates={() => console.log("打开模板")}
            onOpenStickers={() => setStickerPanelOpen(true)}
          />
        )
      })()}
      {/* 背景设置面板 */}
      <BackgroundPanel
        open={backgroundPanelOpen}
        onOpenChange={setBackgroundPanelOpen}
        onBackgroundChange={setBackground}
      />
      {/* 图片选择面板 */}
      <ImagePanel
        open={imagePanelOpen}
        onOpenChange={(open) => {
          if (!open) {
            // 面板关闭时重置替换目标
            setReplaceTarget(null)
          }
          setImagePanelOpen(open)
        }}
        onImageSelect={async (src) => {
          if (replaceTarget) {
            // 执行图片替换逻辑
            try {
              const newImgNode = await createImageFromUrl(src)
              replaceTarget.update({ image: newImgNode.image() })
              editor?.board.draw()
            } catch (err) {
              console.error("替换图片失败", err)
            }
            setReplaceTarget(null)
            setImagePanelOpen(false)
            return
          }

          // 默认行为：新增图片
          addImage(src, {
            x: Math.random() * (canvasSize.width - 200),
            y: Math.random() * (canvasSize.height - 200),
          })
          setImagePanelOpen(false)
        }}
      />
      {/* 贴纸选择面板 */}
      <StickerPanel
        open={stickerPanelOpen}
        onOpenChange={setStickerPanelOpen}
        onStickerSelect={(src) => {
          addImage(src, {
            x: Math.random() * (canvasSize.width - 200),
            y: Math.random() * (canvasSize.height - 200),
          })
          setStickerPanelOpen(false)
        }}
      />

      {/* 移动端属性面板 */}
      {isMobile && (
        <Drawer.Root
          open={propertyPanelOpen}
          onOpenChange={setPropertyPanelOpen}
        >
          <Drawer.Portal>
            <Drawer.Overlay className="fixed inset-0 bg-black/40 z-40" />
            <Drawer.Content className="bg-white h-[70vh] fixed bottom-0 left-0 right-0 outline-none rounded-t-xl z-50">
              <div className="flex flex-col h-full">
                <div className="flex-shrink-0 mx-auto w-12 h-1.5 bg-gray-300 rounded-full mt-3 mb-4" />
                <div className="flex-1 overflow-hidden">
                  <PropertyPanel
                    selectedShapes={selectedShapes}
                    onDelete={() => {
                      deleteSelected()
                      setPropertyPanelOpen(false)
                    }}
                    onDuplicate={() => {
                      duplicateSelected()
                      setPropertyPanelOpen(false)
                    }}
                    isMobile={true}
                  />
                </div>
              </div>
            </Drawer.Content>
          </Drawer.Portal>
        </Drawer.Root>
      )}
    </div>
  )
}
