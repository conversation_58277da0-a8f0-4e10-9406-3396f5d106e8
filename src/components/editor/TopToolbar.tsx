import { ExportBoard } from "@/components/ExportBoard"
import { ImportBoard } from "@/components/ImportBoard"
import type { <PERSON><PERSON><PERSON> } from "@/components/pikaso/index.all"
import { Button } from "@/components/ui/button"
import { useNavigate } from "@tanstack/react-router"
import { ArrowLeft, Eye, Redo, Save, Undo } from "lucide-react"

interface TopToolbarProps {
  editor: Pikaso | null
  onSave?: () => void
  onPreview?: () => void
}

export const TopToolbar = ({ editor, onSave, onPreview }: TopToolbarProps) => {
  const navigate = useNavigate()

  // 处理返回相册管理
  const handleBackToAlbum = () => {
    navigate({ to: "/album" })
  }

  return (
    <div className="flex justify-between items-center px-2 py-2 bg-white border-b border-gray-200">
      {/* Left side container */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleBackToAlbum}
        className="flex items-center space-x-1"
      >
        <ArrowLeft size={16} />
        <span>返回相册</span>
      </Button>
      <div className="flex items-center justify-center space-x-1 sm:space-x-2">
        {/* Undo/Redo */}
        <div className="flex items-center justify-center space-x-1">
          <Button
            size="sm"
            variant="outline"
            onClick={() => editor?.undo()}
            disabled={!editor}
            className="min-w-0 px-2"
          >
            <Undo size={16} className="sm:mr-1" />
            <span className="hidden sm:inline">撤销</span>
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => editor?.redo()}
            disabled={!editor}
            className="min-w-0 px-2"
          >
            <Redo size={16} className="sm:mr-1" />
            <span className="hidden sm:inline">重做</span>
          </Button>
        </div>

        {/* Import/Export - Desktop only */}
        <div className="hidden sm:flex items-center space-x-1 ml-4">
          <ImportBoard editor={editor} />
          <ExportBoard editor={editor} />
        </div>
      </div>

      {/* Right side container */}
      <div className="flex items-center space-x-1 sm:space-x-2">
        {/* Preview Button - Mobile */}
        <Button
          size="sm"
          variant="outline"
          onClick={onPreview}
          className="sm:hidden"
        >
          <Eye size={16} />
        </Button>
        {/* Preview Button - Desktop */}
        <Button
          size="sm"
          variant="default"
          onClick={onPreview}
          className="hidden sm:flex"
        >
          <Eye size={16} className="mr-1" />
          <span>预览</span>
        </Button>

        {/* Save Button - Combined */}
        <Button
          size="sm"
          variant="default"
          className="bg-green-600 hover:bg-green-700"
          onClick={onSave}
        >
          <Save size={16} className="sm:mr-1" />
          <span className="hidden sm:inline">保存</span>
        </Button>
      </div>
    </div>
  )
}
