import { ChevronLeft, ChevronRight } from "lucide-react"

interface PageNavigationProps {
  onPrevPage?: () => void
  onNextPage?: () => void
  canGoPrev?: boolean
  canGoNext?: boolean
  currentPageInfo?: string
}

export const PageNavigation = ({
  onPrevPage,
  onNextPage,
  canGoPrev = false,
  canGoNext = false,
  currentPageInfo,
}: PageNavigationProps) => {
  return (
    <div className="flex items-center justify-center px-2 py-3">
      {/* 左侧：上一页按钮 */}
      <button
        type="button"
        className={`p-2  ${
          canGoPrev ? "text-indigo-700" : "text-stone-300 cursor-not-allowed"
        }`}
        onClick={onPrevPage}
        disabled={!canGoPrev}
      >
        <ChevronLeft size={24} className="flex-shrink-0" />
      </button>

      {/* 页面信息显示 */}
      {currentPageInfo && (
        <div className="text-center text-xs bg-white rounded-full px-4 py-2 shadow-sm">
          {currentPageInfo}
        </div>
      )}

      {/* 右侧：下一页按钮 */}
      <button
        type="button"
        className={` p-2  ${
          canGoNext ? "text-indigo-700" : "text-stone-300 cursor-not-allowed"
        }`}
        onClick={onNextPage}
        disabled={!canGoNext}
      >
        <ChevronRight size={24} className="flex-shrink-0" />
      </button>
    </div>
  )
}
