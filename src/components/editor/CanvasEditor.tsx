import type { <PERSON><PERSON><PERSON> } from "@/components/pikaso/index.all"
import { resize } from "motion"
import { forwardRef, useEffect } from "react"

interface CanvasEditorProps {
  editor: Pikaso | null
  canvasSize: { width: number; height: number }
}

export const CanvasEditor = forwardRef<HTMLDivElement, CanvasEditorProps>(
  ({ editor, canvasSize }, ref) => {
    console.log("canvasSize", canvasSize)
    // 计算画布宽高比
    const aspectRatio = canvasSize.height / canvasSize.width

    useEffect(() => {
      // 处理画布容器和Pikaso板的尺寸调整
      if (!editor || !ref || typeof ref === "function" || !ref.current) {
        return
      }

      const containerElement = ref.current

      const setSizeAndRescale = (currentWidth: number) => {
        console.log("🚀 ~ currentWidth:", currentWidth)
        if (currentWidth <= 0) {
          // 如果宽度无效，则跳过调整大小
          containerElement.style.height = "0px"
          return
        }
        const newHeight = currentWidth * aspectRatio
        // containerElement.style.width = `${currentWidth}px`
        containerElement.style.height = `${newHeight}px`
        // Pikaso的board.rescale()应在其容器div具有新尺寸后调用
        editor.board.rescale()
      }

      // 设置观察器以监控容器元素的大小变化
      const stopObserving = resize(containerElement, (_, info) => {
        console.log("🚀 ~ info:", info)
        setSizeAndRescale(info.width)
      })

      // 初始尺寸设置：
      // 组件已渲染，containerElement 应具有由CSS确定的初始宽度
      const initialWidth = containerElement.offsetWidth
      if (initialWidth > 0) {
        setSizeAndRescale(initialWidth)
      }

      return () => {
        // 清理观察器
        stopObserving()
      }
    }, [editor, ref, aspectRatio])

    return (
      <div className="canvas-container flex flex-1 justify-center items-center p-2 md:p-4 w-screen md:w-auto">
        <div
          ref={ref}
          className="w-full md:w-auto relative bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden"
        />
      </div>
    )
  },
)

CanvasEditor.displayName = "CanvasEditor"
