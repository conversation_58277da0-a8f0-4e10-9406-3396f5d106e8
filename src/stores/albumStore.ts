import type { Album, AlbumSize, AlbumTemplate, Page } from "@/types/album"
import { nanoid } from "nanoid"
import { temporal } from "zundo"
import type { TemporalState } from "zundo"
import { create } from "zustand"
import { createJSONStorage, persist } from "zustand/middleware"
import { useStoreWithEqualityFn } from "zustand/traditional"

interface AlbumState {
  album: Album | null
  createAlbum: (
    name: string,
    template?: AlbumTemplate,
    size?: AlbumSize,
  ) => Album
  updateAlbumName: (name: string) => void
  insertPageAfter: (afterPageId: string) => void
  deletePage: (pageId: string) => void
  setCurrentPage: (pageId: string | null) => void
  updatePage: (pageId: string, content: Partial<Page>) => void
  reorderPages: (sourceIndex: number, destinationIndex: number) => void

  exportAlbum: () => string
  importAlbum: (data: string) => boolean
}

const createInitialPage = (
  index: number,
  type: Page["type"] = "content",
): Page => ({
  id: nanoid(),
  index,
  type,
  content: {
    stage: {
      attrs: { width: 500, height: 500, x: 0, y: 0 },
      filters: [],
      className: "Stage",
    },
    layer: {
      attrs: { x: 0, y: 0, width: 500, height: 500 },
      filters: [],
      className: "Layer",
    },
    background: {
      image: {
        attrs: { x: 0, y: 0 },
        filters: [],
        className: "Image",
        zIndex: 0,
      },
      overlay: {
        attrs: { x: 0, y: 0 },
        filters: [],
        className: "Rect",
        zIndex: 1,
      },
    },
    shapes: [
      {
        attrs: {
          fill: "red",
          radius: 50,
          x: 500,
          y: 100,
          width: 100,
          height: 100,
        },
        filters: [],
        className: "Circle",
        zIndex: 4,
      },
      {
        attrs: { x: 600, y: 300, width: 407.81, height: 40 },
        filters: [],
        className: "Label",
        zIndex: 5,
        children: [
          {
            attrs: { fill: "transparent", width: 407.8125, height: 40 },
            filters: [],
            className: "Tag",
            zIndex: 0,
          },
          {
            attrs: {
              text: "Pikaso is great, isn't it?",
              fontSize: 40,
              fontWeight: "bold",
              fill: "purple",
              height: "auto",
            },
            filters: [],
            className: "Text",
            zIndex: 0,
          },
        ],
      },
      {
        attrs: {
          width: 250,
          height: 250,
          x: 100,
          y: 100,
          url: "https://unicorn-media.ancda.com/prod/growthArchives/78492111288139808/parent/88357351911850006/2025-05-09/rmGcEYKs.jpg",
        },
        filters: [],
        className: "Image",
        zIndex: 6,
      },
    ],
  },
  metadata: {
    createdAt: Date.now(),
    updatedAt: Date.now(),
    version: "1.0.0",
  },
})

const createCoverPage = (): Page => ({
  id: nanoid(),
  index: 1,
  type: "cover",
  content: {
    stage: {
      attrs: {
        width: 2480,
        height: 2480,
        x: 0,
        y: 0,
      },
      filters: [],
      className: "Stage",
    },
    layer: {
      attrs: {
        x: 0,
        y: 0,
        width: 2480,
        height: 2480,
      },
      filters: [],
      className: "Layer",
    },
    background: {
      image: {
        attrs: {
          x: 0,
          y: 0,
        },
        filters: [],
        className: "Image",
        zIndex: 0,
      },
      overlay: {
        attrs: {
          x: 0,
          y: 0,
        },
        filters: [],
        className: "Rect",
        zIndex: 1,
      },
    },
    shapes: [
      {
        attrs: {
          width: 1500,
          height: 1500,
          x: 53,
          y: 149,
          lastCropUsed: "center-middle",
          cropY: 250,
          cropWidth: 1500,
          cropHeight: 1500,
          url: "https://unicorn-media.obs.cn-north-4.myhuaweicloud.com/test/growthArchives/demo/72252-4368354-2_jpg1.jpeg",
        },
        filters: [],
        className: "Image",
        zIndex: 4,
      },
    ],
  },
  metadata: {
    createdAt: Date.now(),
    updatedAt: Date.now(),
    version: "1.0.0",
  },
})
const createBackCoverPage = (index: number): Page =>
  createInitialPage(index, "back-cover")

const recalculatePageIndices = (pages: Page[]): Page[] => {
  return pages.map((page, index) => ({
    ...page,
    index: index + 1,
    metadata: {
      ...page.metadata,
      updatedAt: Date.now(),
    },
  }))
}

const ensureCorrectPageStructure = (pages: Page[]): Page[] => {
  if (pages.length === 0) return pages

  const updatedPages = [...pages]

  // 确保第一页是封面
  if (updatedPages[0] && updatedPages[0].type !== "cover") {
    updatedPages[0] = { ...updatedPages[0], type: "cover" }
  }

  // 处理中间页面为内容页（第二页开始）
  for (let i = 1; i < updatedPages.length; i++) {
    if (
      updatedPages[i].type !== "content" &&
      updatedPages[i].type !== "back-cover"
    ) {
      updatedPages[i] = { ...updatedPages[i], type: "content" }
    }
  }

  // 处理最后一页的逻辑：如果总页数大于等于5，最后一页设为封底
  if (updatedPages.length >= 5) {
    const lastIndex = updatedPages.length - 1
    updatedPages[lastIndex] = {
      ...updatedPages[lastIndex],
      type: "back-cover",
    }
  }

  return recalculatePageIndices(updatedPages)
}

export const useAlbumStore = create<AlbumState>()(
  persist(
    temporal(
      (set, get) => ({
        album: null,

        createAlbum: (
          name: string,
          template?: AlbumTemplate,
          size?: AlbumSize,
        ) => {
          // 创建初始页面：封面 + 3页内容页 + 封底
          const initialPages: Page[] = [
            createCoverPage(), // 封面
            createInitialPage(2, "content"), // 内容页1
            createInitialPage(3, "content"), // 内容页2
            createInitialPage(4, "content"), // 内容页3
            createInitialPage(5, "back-cover"), // 封底
          ]

          const album: Album = {
            id: nanoid(),
            name,
            pages: initialPages,
            currentPageId: null,
            metadata: {
              createdAt: Date.now(),
              updatedAt: Date.now(),
              version: "1.0.0",
              totalPages: initialPages.length,
              template,
            },
          }

          set({ album })

          return album
        },

        updateAlbumName: (name: string) => {
          set((state) => {
            if (!state.album) return state

            const updatedAlbum = {
              ...state.album,
              name: name.trim(),
              metadata: {
                ...state.album.metadata,
                updatedAt: Date.now(),
              },
            }

            return { album: updatedAlbum }
          })
        },

        insertPageAfter: (afterPageId: string) => {
          set((state) => {
            if (!state.album) return state

            const afterPageIndex = state.album.pages.findIndex(
              (page) => page.id === afterPageId,
            )

            if (afterPageIndex === -1) return state

            const insertPosition = afterPageIndex + 1
            const newPage = createInitialPage(insertPosition + 1, "content")

            const updatedPages = [...state.album.pages]
            updatedPages.splice(insertPosition, 0, newPage)

            const finalPages = ensureCorrectPageStructure(updatedPages)

            const updatedAlbum = {
              ...state.album,
              pages: finalPages,
              currentPageId: newPage.id,
              metadata: {
                ...state.album.metadata,
                updatedAt: Date.now(),
                totalPages: finalPages.length,
              },
            }

            return { album: updatedAlbum }
          })
        },

        deletePage: (pageId: string) => {
          const state = get()
          if (!state.album) return

          const pageToDelete = state.album.pages.find((p) => p.id === pageId)
          if (!pageToDelete) return

          set((state) => {
            if (!state.album) return state

            // 不允许删除封面页
            if (pageToDelete.type === "cover") {
              console.warn("不能删除封面页")
              return state
            }

            // 不允许删除第一个展开页（第一个内容页）
            const contentPages = state.album.pages
              .filter((page) => page.type === "content")
              .sort((a, b) => a.index - b.index)

            if (
              contentPages.length > 0 &&
              pageToDelete.id === contentPages[0].id
            ) {
              console.warn("不能删除第一个展开页")
              return state
            }

            const updatedPages = state.album.pages.filter(
              (p) => p.id !== pageId,
            )
            const finalPages = ensureCorrectPageStructure(updatedPages)

            return {
              album: {
                ...state.album,
                pages: finalPages,
                currentPageId:
                  pageId === state.album.currentPageId
                    ? null
                    : state.album.currentPageId,
                metadata: {
                  ...state.album.metadata,
                  updatedAt: Date.now(),
                  totalPages: finalPages.length,
                },
              },
            }
          })
        },

        setCurrentPage: (pageId: string | null) => {
          set((state) => {
            if (!state.album) return state
            return {
              album: {
                ...state.album,
                currentPageId: pageId,
              },
            }
          })
        },

        updatePage: (pageId: string, content: Partial<Page>) => {
          set((state) => {
            if (!state.album) return state

            const updatedPages = state.album.pages.map((page) =>
              page.id === pageId
                ? {
                    ...page,
                    ...content,
                    metadata: {
                      ...page.metadata,
                      updatedAt: Date.now(),
                    },
                  }
                : page,
            )

            return {
              album: {
                ...state.album,
                pages: updatedPages,
                metadata: {
                  ...state.album.metadata,
                  updatedAt: Date.now(),
                },
              },
            }
          })
        },

        reorderPages: (sourceIndex: number, destinationIndex: number) => {
          set((state) => {
            if (!state.album) return state

            // 不允许移动封面页
            if (sourceIndex === 0) {
              console.warn("不能移动封面页")
              return state
            }

            const newPages = [...state.album.pages]
            const [removed] = newPages.splice(sourceIndex, 1)
            newPages.splice(destinationIndex, 0, removed)

            const finalPages = ensureCorrectPageStructure(newPages)

            return {
              album: {
                ...state.album,
                pages: finalPages,
                metadata: {
                  ...state.album.metadata,
                  updatedAt: Date.now(),
                },
              },
            }
          })
        },

        exportAlbum: () => {
          const state = get()
          if (!state.album) return ""

          try {
            return JSON.stringify(state.album, null, 2)
          } catch (error) {
            console.error("Failed to export album:", error)
            return ""
          }
        },

        importAlbum: (data: string) => {
          try {
            const album = JSON.parse(data) as Album
            set({ album })
            return true
          } catch (error) {
            console.error("Failed to import album:", error)
            return false
          }
        },
      }),
      {
        limit: 50,
        partialize: (state) => ({
          album: state.album,
        }),
        equality: (pastState, currentState) => {
          return JSON.stringify(pastState) === JSON.stringify(currentState)
        },
      },
    ),
    {
      name: "album-data",
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        album: state.album,
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.album) {
          const album = state.album

          if (!album.pages || !Array.isArray(album.pages)) {
            console.warn(
              "Album pages is not an array, initializing as empty array",
            )
            album.pages = []
          }

          if (!album.metadata) {
            album.metadata = {
              createdAt: Date.now(),
              updatedAt: Date.now(),
              version: "1.0.0",
              totalPages: album.pages.length,
            }
          }
        }
      },
    },
  ),
)

export const useAlbumStoreUndo = () => {
  const { undo, redo, clear } = useAlbumStore.temporal.getState()
  return { undo, redo, clear }
}

export const useAlbumStoreCanUndoRedo = () => {
  return useStoreWithEqualityFn(
    useAlbumStore.temporal,
    (state: TemporalState<{ album: Album | null }>) => ({
      canUndo: state.pastStates.length > 0,
      canRedo: state.futureStates.length > 0,
    }),
  )
}
